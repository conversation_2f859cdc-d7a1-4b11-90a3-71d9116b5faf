{"name": "百度云线上灰度测试插件", "version": "3.0", "manifest_version": 3, "description": "百度云线上灰度测试的Chrome插件，仅对百度云、百度云沙盒的页面生效：(1)设置fe灰度版本号;(2)为每个http请求自动化修改x-bce-request-id、用于灰度分流", "icons": {"16": "bce.png", "48": "bce.png", "128": "bce.png"}, "background": {"service_worker": "bce_canary_plugin.js"}, "permissions": ["declarativeNetRequest", "declarativeNetRequestWithHostAccess", "tabs", "storage", "activeTab", "scripting"], "host_permissions": ["https://localhost.qasandbox.bcetest.baidu.com:8088/*", "https://qasandbox.bcetest.baidu.com/*", "https://cloud.baidu.com/*", "https://console.bce.baidu.com/*", "http://canary.baidu-int.com:8000/*", "https://canary.baidu-int.com:8000/*", "http://bjyz-faasqa-online-test-env1.epc.baidu.com:8003/*", "http://bce.console.baidu-int.com/*", "http://opscenter.bce-sandbox.baidu.com/*", "http://sandbox.bce.console.baidu-int.com/*", "http://***********:8173/*", "http://***********:8174/*", "http://***********:8170/*", "http://*************:8171/*", "http://***********:8183/*", "http://amis.baidu.com/*", "http://wiki.baidu.com/*", "<all_urls>"], "action": {"default_icon": "bce.png", "default_popup": "popup.html"}, "declarative_net_request": {"rule_resources": [{"id": "ruleset_1", "enabled": true, "path": "rules.json"}]}}