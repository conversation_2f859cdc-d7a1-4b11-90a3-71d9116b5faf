# 百度云线上灰度测试插件 V3 版本安装指南

## 快速安装

### 1. 准备工作
- 确保Chrome浏览器版本 >= 88
- 确保已启用开发者模式

### 2. 安装步骤

1. **打开Chrome扩展管理页面**
   - 在地址栏输入：`chrome://extensions/`
   - 或者：菜单 → 更多工具 → 扩展程序

2. **启用开发者模式**
   - 在页面右上角找到"开发者模式"开关
   - 点击开启开发者模式

3. **加载插件**
   - 点击"加载已解压的扩展程序"按钮
   - 选择 `new` 文件夹（包含manifest.json的文件夹）
   - 点击"选择文件夹"

4. **验证安装**
   - 插件应该出现在扩展程序列表中
   - 插件名称：百度云线上灰度测试插件
   - 版本：3.0
   - 状态：已启用

### 3. 使用方法

1. **基本设置**
   - 点击浏览器工具栏中的插件图标
   - 在弹出窗口中设置：
     - 项目标签（4位字符）
     - 环境标签（4位字符）  
     - 分流标签（12位字符）
     - FE版本（可选）

2. **启用功能**
   - 设置完参数后，点击开关按钮启用
   - 插件会自动为HTTP请求添加X-Bce-Request-Id头

3. **功能测试**
   - 打开 `test.html` 文件进行功能测试
   - 或访问百度云相关页面验证功能

## 故障排除

### 常见问题

1. **插件无法加载**
   - 检查Chrome版本是否支持Manifest V3
   - 确保所有文件都在new文件夹中
   - 检查manifest.json语法是否正确

2. **插件图标不显示**
   - 确保bce.png文件存在
   - 重新加载插件

3. **功能不工作**
   - 检查浏览器控制台是否有错误
   - 确保已正确设置所有必需参数
   - 验证目标网站是否在host_permissions列表中

4. **Service Worker错误**
   - 在chrome://extensions/页面点击插件的"详细信息"
   - 查看"检查视图"中的Service Worker
   - 检查控制台错误信息

### 调试方法

1. **检查Service Worker**
   ```
   chrome://extensions/ → 插件详情 → Service Worker → 检查
   ```

2. **检查存储数据**
   ```
   chrome://extensions/ → 插件详情 → 存储 → 查看存储数据
   ```

3. **网络请求验证**
   - 打开开发者工具 → Network面板
   - 发送请求查看X-Bce-Request-Id头是否存在

## 与V2版本的区别

| 功能 | V2版本 | V3版本 |
|------|--------|--------|
| 后台脚本 | Background Script | Service Worker |
| 存储方式 | localStorage | chrome.storage.local |
| 请求拦截 | webRequest | declarativeNetRequest |
| 脚本注入 | tabs.executeScript | scripting.executeScript |

## 技术支持

如果遇到问题，请：
1. 查看README.md了解详细信息
2. 检查浏览器控制台错误
3. 确认Chrome版本兼容性
4. 联系开发团队获取支持

---

**注意**: 此插件仅用于百度云内部灰度测试，请勿在生产环境外使用。
