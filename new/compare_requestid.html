<!DOCTYPE html>
<html>
<head>
    <title>V2 vs V3 RequestId 对比测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { display: flex; gap: 20px; }
        .version { flex: 1; border: 1px solid #ccc; padding: 15px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        button { padding: 10px 20px; margin: 5px; }
        .v2 { border-color: #ff6b6b; }
        .v3 { border-color: #4ecdc4; }
    </style>
</head>
<body>
    <h1>V2 vs V3 RequestId 生成对比</h1>
    
    <div>
        <h3>测试配置</h3>
        <label>项目标签: <input type="text" id="projectTag" value="test" maxlength="4"></label>
        <label>环境标签: <input type="text" id="envTag" value="gray" maxlength="4"></label>
        <label>分流标签: <input type="text" id="diverterTag" value="bcecanarytag" maxlength="12"></label>
        <label><input type="checkbox" id="canarySwitch" checked> 灰度开关</label>
    </div>
    
    <div class="container">
        <div class="version v2">
            <h3>V2版本 (localStorage)</h3>
            <button onclick="generateV2RequestId()">生成V2 RequestId</button>
            <div id="v2Result" class="result"></div>
        </div>
        
        <div class="version v3">
            <h3>V3版本 (chrome.storage.local)</h3>
            <button onclick="generateV3RequestId()">生成V3 RequestId</button>
            <div id="v3Result" class="result"></div>
        </div>
    </div>
    
    <div>
        <h3>对比结果</h3>
        <button onclick="compareResults()">对比两个版本</button>
        <div id="compareResult" class="result"></div>
    </div>

    <script>
        // V2版本的生成逻辑 (使用localStorage)
        function generateV2RequestId() {
            function chunk() {
                let v = (~~(Math.random() * 0xffff)).toString(16);
                if (v.length < 4) {
                    v += new Array(4 - v.length + 1).join('0');
                }
                return v;
            }

            const a = chunk();
            const b = chunk();
            const c = chunk();
            const d = chunk();
            const e = chunk();
            const f = chunk();
            const g = chunk();
            const h = chunk();

            // 模拟localStorage
            const projectTag = document.getElementById('projectTag').value;
            const envTag = document.getElementById('envTag').value;
            const diverterTag = document.getElementById('diverterTag').value;
            const bceCanarySwitch = document.getElementById('canarySwitch').checked ? 'on' : 'off';

            let requestId;
            if (bceCanarySwitch === 'on') {
                requestId = a + b + '-' + c + '-' + envTag + '-' + projectTag + '-' + diverterTag;
            } else {
                requestId = a + b + '-' + c + '-' + d + '-' + e + '-' + f + g + h;
            }
            
            document.getElementById('v2Result').innerHTML = 
                `<strong>V2 RequestId:</strong><br>${requestId}<br>
                 <small>格式: ${bceCanarySwitch === 'on' ? '灰度模式' : '普通模式'}</small>`;
            
            return requestId;
        }

        // V3版本的生成逻辑 (使用chrome.storage.local模拟)
        async function generateV3RequestId() {
            function chunk() {
                let v = (~~(Math.random() * 0xffff)).toString(16);
                if (v.length < 4) {
                    v += new Array(4 - v.length + 1).join('0');
                }
                return v;
            }

            const a = chunk();
            const b = chunk();
            const c = chunk();
            const d = chunk();
            const e = chunk();
            const f = chunk();
            const g = chunk();
            const h = chunk();

            // 模拟chrome.storage.local
            const projectTag = document.getElementById('projectTag').value;
            const envTag = document.getElementById('envTag').value;
            const diverterTag = document.getElementById('diverterTag').value;
            const bceCanarySwitch = document.getElementById('canarySwitch').checked ? 'on' : 'off';

            let requestId;
            if (bceCanarySwitch === 'on') {
                requestId = a + b + '-' + c + '-' + envTag + '-' + projectTag + '-' + diverterTag;
            } else {
                requestId = a + b + '-' + c + '-' + d + '-' + e + '-' + f + g + h;
            }
            
            document.getElementById('v3Result').innerHTML = 
                `<strong>V3 RequestId:</strong><br>${requestId}<br>
                 <small>格式: ${bceCanarySwitch === 'on' ? '灰度模式' : '普通模式'}</small>`;
            
            return requestId;
        }

        function compareResults() {
            const v2Id = generateV2RequestId();
            const v3Id = generateV3RequestId();
            
            const v2Parts = v2Id.split('-');
            const v3Parts = v3Id.split('-');
            
            let comparison = '<h4>格式对比:</h4>';
            comparison += `<p>V2: ${v2Parts.length}个部分 - ${v2Parts.join(' | ')}</p>`;
            comparison += `<p>V3: ${v3Parts.length}个部分 - ${v3Parts.join(' | ')}</p>`;
            
            if (v2Parts.length === v3Parts.length) {
                comparison += '<p style="color: green;">✅ 格式一致</p>';
                
                // 检查灰度模式下的固定部分
                if (document.getElementById('canarySwitch').checked && v2Parts.length === 5) {
                    const v2Fixed = [v2Parts[2], v2Parts[3], v2Parts[4]];
                    const v3Fixed = [v3Parts[2], v3Parts[3], v3Parts[4]];
                    
                    if (JSON.stringify(v2Fixed) === JSON.stringify(v3Fixed)) {
                        comparison += '<p style="color: green;">✅ 灰度参数一致</p>';
                    } else {
                        comparison += '<p style="color: red;">❌ 灰度参数不一致</p>';
                        comparison += `<p>V2固定部分: ${v2Fixed.join('-')}</p>`;
                        comparison += `<p>V3固定部分: ${v3Fixed.join('-')}</p>`;
                    }
                }
            } else {
                comparison += '<p style="color: red;">❌ 格式不一致</p>';
            }
            
            document.getElementById('compareResult').innerHTML = comparison;
        }

        // 实时更新配置
        document.getElementById('projectTag').addEventListener('input', function() {
            if (this.value.length > 4) this.value = this.value.slice(0, 4);
        });
        
        document.getElementById('envTag').addEventListener('input', function() {
            if (this.value.length > 4) this.value = this.value.slice(0, 4);
        });
        
        document.getElementById('diverterTag').addEventListener('input', function() {
            if (this.value.length > 12) this.value = this.value.slice(0, 12);
        });
    </script>
</body>
</html>
