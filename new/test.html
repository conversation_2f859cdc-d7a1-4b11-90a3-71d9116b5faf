<!DOCTYPE html>
<html>
<head>
    <title>BCE Canary Plugin V3 Test</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>百度云灰度测试插件 V3 版本测试页面</h1>
    
    <h2>功能测试</h2>
    <div>
        <h3>1. 灰度分流功能</h3>
        <p>请打开插件popup，设置项目标签、环境标签、分流标签，然后开启开关</p>
        <button onclick="checkRequestId()">检查X-Bce-Request-Id</button>
        <div id="requestIdResult"></div>
    </div>
    
    <div>
        <h3>2. FE版本设置功能</h3>
        <p>请在插件中设置FE版本，然后检查sessionStorage</p>
        <button onclick="checkSessionStorage()">检查SessionStorage</button>
        <div id="sessionStorageResult"></div>
    </div>
    
    <div>
        <h3>3. 存储功能测试</h3>
        <button onclick="checkStorage()">检查Chrome Storage</button>
        <div id="storageResult"></div>
    </div>

    <script>
        function checkRequestId() {
            // 发送一个测试请求来检查X-Bce-Request-Id是否被添加
            const testUrl = 'https://httpbin.org/headers';

            fetch(testUrl, {
                method: 'GET'
            }).then(response => response.json())
            .then(data => {
                const headers = data.headers;
                const requestId = headers['X-Bce-Request-Id'] || headers['x-bce-request-id'];

                if (requestId) {
                    document.getElementById('requestIdResult').innerHTML =
                        `<p style="color: green;">✅ X-Bce-Request-Id 头已添加: <strong>${requestId}</strong></p>
                         <p>格式检查: ${validateRequestIdFormat(requestId)}</p>`;
                } else {
                    document.getElementById('requestIdResult').innerHTML =
                        '<p style="color: red;">❌ 未找到 X-Bce-Request-Id 头</p>' +
                        '<p>所有请求头: ' + JSON.stringify(headers, null, 2) + '</p>';
                }
            }).catch(error => {
                document.getElementById('requestIdResult').innerHTML =
                    '<p style="color: orange;">⚠️ 请求失败，请检查开发者工具的Network面板查看X-Bce-Request-Id头</p>' +
                    '<p>错误: ' + error.message + '</p>';
            });
        }

        function validateRequestIdFormat(requestId) {
            // 检查格式是否符合预期: xxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
            const parts = requestId.split('-');
            if (parts.length === 5) {
                return `<span style="color: green;">✅ 格式正确 (灰度模式): ${parts[0]}-${parts[1]}-${parts[2]}-${parts[3]}-${parts[4]}</span>`;
            } else if (parts.length === 6) {
                return `<span style="color: blue;">ℹ️ 格式正确 (普通模式): ${parts[0]}-${parts[1]}-${parts[2]}-${parts[3]}-${parts[4]}${parts[5]}</span>`;
            } else {
                return `<span style="color: red;">❌ 格式异常: 期望5或6个部分，实际${parts.length}个</span>`;
            }
        }
        
        function checkSessionStorage() {
            let result = '<h4>SessionStorage内容:</h4><ul>';
            for (let i = 0; i < sessionStorage.length; i++) {
                let key = sessionStorage.key(i);
                let value = sessionStorage.getItem(key);
                result += `<li>${key}: ${value}</li>`;
            }
            result += '</ul>';
            document.getElementById('sessionStorageResult').innerHTML = result;
        }
        
        function checkStorage() {
            if (chrome && chrome.storage) {
                chrome.storage.local.get(null, (items) => {
                    let result = '<h4>Chrome Storage内容:</h4><ul>';
                    for (let key in items) {
                        result += `<li>${key}: ${items[key]}</li>`;
                    }
                    result += '</ul>';
                    document.getElementById('storageResult').innerHTML = result;
                });
            } else {
                document.getElementById('storageResult').innerHTML = 
                    '<p>Chrome Storage API不可用，请在Chrome扩展环境中测试</p>';
            }
        }
    </script>
</body>
</html>
