# 百度云线上灰度测试插件 V3 版本

## 概述
这是百度云线上灰度测试插件的Chrome Extension Manifest V3版本，保留了V2版本的所有功能，并升级到了最新的Chrome扩展标准。

## 主要功能
1. **灰度分流功能**: 自动为HTTP请求添加X-Bce-Request-Id头，用于灰度分流
2. **FE版本设置**: 设置前端灰度版本号到sessionStorage
3. **流量抓取**: 支持流量抓取任务的创建和管理
4. **回放功能**: 支持流量回放任务的执行和监控

## V3版本主要变化

### 1. Manifest文件变化
- `manifest_version`: 从2升级到3
- `background`: 从`scripts`改为`service_worker`
- `browser_action`: 改为`action`
- `permissions`: 调整为V3格式，添加了`declarativeNetRequest`等新权限
- `host_permissions`: 从`permissions`中分离出来

### 2. API变化
- **webRequest API**: 使用`chrome.declarativeNetRequest`替代`webRequestBlocking`
- **Storage API**: 使用`chrome.storage.local`替代`localStorage`
- **Scripting API**: 使用`chrome.scripting.executeScript`替代`chrome.tabs.executeScript`

### 3. Service Worker
- 后台脚本改为Service Worker模式
- 处理Service Worker的生命周期管理
- 异步处理所有存储操作

## 文件结构
```
new/
├── manifest.json          # V3版本的manifest文件
├── bce_canary_plugin.js   # Service Worker主文件
├── popup.html             # 弹出窗口HTML
├── popup_v3.js           # V3版本的弹出窗口逻辑
├── rules.json            # declarativeNetRequest规则文件
├── bce.png               # 插件图标
├── test.html             # 功能测试页面
└── README.md             # 说明文档
```

## 安装和使用

### 1. 安装插件
1. 打开Chrome浏览器
2. 进入扩展程序管理页面 (chrome://extensions/)
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择`new`目录

### 2. 使用方法
使用方法与V2版本完全相同：

1. **设置灰度参数**:
   - 点击插件图标打开弹窗
   - 设置项目标签（4位字符）
   - 设置环境标签（4位字符）
   - 设置分流标签（12位字符）
   - 可选：设置FE版本号

2. **启用灰度功能**:
   - 开启插件开关
   - 插件会自动为所有HTTP请求添加X-Bce-Request-Id头

3. **流量抓取和回放**:
   - 切换到对应的Tab页面
   - 按照界面提示操作

## 兼容性说明
- 支持Chrome 88+版本
- 保持与V2版本完全相同的用户界面和功能
- 所有配置和数据存储格式保持兼容

## 开发和调试
1. 使用`test.html`页面测试基本功能
2. 在Chrome开发者工具中查看Network面板验证请求头
3. 检查Service Worker的Console输出进行调试

## 注意事项
1. V3版本的Service Worker有生命周期限制，插件会自动处理
2. 所有存储操作都是异步的，确保正确处理Promise
3. declarativeNetRequest有规则数量限制，当前实现使用动态规则更新

## 故障排除
如果插件不工作，请检查：
1. Chrome版本是否支持Manifest V3
2. 是否正确加载了所有文件
3. 在chrome://extensions/页面查看是否有错误信息
4. 检查Service Worker的Console输出
